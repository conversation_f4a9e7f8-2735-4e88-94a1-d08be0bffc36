import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'app_design_system.dart';

/// 应用主题配置
/// 基于设计系统创建完整的应用主题
class AppTheme {
  AppTheme._();

  /// 深色主题（主要主题）
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      
      // 颜色方案
      colorScheme: ColorScheme.dark(
        primary: AppDesignSystem.primaryBlue,
        primaryContainer: AppDesignSystem.primaryBlueAccent,
        secondary: AppDesignSystem.primaryBlueAccent,
        surface: AppDesignSystem.backgroundPrimary,
        surfaceContainer: AppDesignSystem.backgroundSecondary,
        surfaceContainerHighest: AppDesignSystem.backgroundCard,
        onPrimary: AppDesignSystem.textOnPrimary,
        onSurface: AppDesignSystem.textPrimary,
        onSurfaceVariant: AppDesignSystem.textSecondary,
        outline: AppDesignSystem.borderPrimary,
        error: AppDesignSystem.error,
      ),
      
      // 脚手架背景色
      scaffoldBackgroundColor: AppDesignSystem.backgroundPrimary,
      
      // AppBar主题
      appBarTheme: AppDesignSystem.appBarTheme,
      
      // 按钮主题
      elevatedButtonTheme: AppDesignSystem.elevatedButtonTheme,
      textButtonTheme: AppDesignSystem.textButtonTheme,
      
      // 输入框主题
      inputDecorationTheme: AppDesignSystem.inputDecorationTheme,
      
      // 底部导航栏主题
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: AppDesignSystem.backgroundPrimary,
        selectedItemColor: AppDesignSystem.primaryBlue,
        unselectedItemColor: AppDesignSystem.textTertiary,
        type: BottomNavigationBarType.fixed,
        elevation: 0,
      ),
      
      // 卡片主题
      cardTheme: CardTheme(
        color: AppDesignSystem.backgroundCard,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: AppDesignSystem.borderRadiusL,
        ),
        shadowColor: Colors.black.withValues(alpha: 0.1),
      ),
      
      // 对话框主题
      dialogTheme: DialogTheme(
        backgroundColor: AppDesignSystem.backgroundCard,
        shape: RoundedRectangleBorder(
          borderRadius: AppDesignSystem.borderRadiusL,
        ),
        titleTextStyle: AppDesignSystem.headingMedium,
        contentTextStyle: AppDesignSystem.bodyMedium,
      ),
      
      // 文本主题
      textTheme: const TextTheme(
        displayLarge: AppDesignSystem.headingLarge,
        displayMedium: AppDesignSystem.headingMedium,
        displaySmall: AppDesignSystem.headingSmall,
        headlineLarge: AppDesignSystem.headingLarge,
        headlineMedium: AppDesignSystem.headingMedium,
        headlineSmall: AppDesignSystem.headingSmall,
        titleLarge: AppDesignSystem.headingMedium,
        titleMedium: AppDesignSystem.headingSmall,
        titleSmall: AppDesignSystem.bodyLarge,
        bodyLarge: AppDesignSystem.bodyLarge,
        bodyMedium: AppDesignSystem.bodyMedium,
        bodySmall: AppDesignSystem.bodySmall,
        labelLarge: AppDesignSystem.buttonText,
        labelMedium: AppDesignSystem.bodyMedium,
        labelSmall: AppDesignSystem.caption,
      ),
      
      // 图标主题
      iconTheme: const IconThemeData(
        color: AppDesignSystem.textPrimary,
        size: AppDesignSystem.iconSizeL,
      ),
      
      // 分割线主题
      dividerTheme: const DividerThemeData(
        color: AppDesignSystem.borderPrimary,
        thickness: 0.5,
        space: 1,
      ),
      
      // 开关主题
      switchTheme: SwitchThemeData(
        thumbColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return AppDesignSystem.primaryBlue;
          }
          return AppDesignSystem.textTertiary;
        }),
        trackColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return AppDesignSystem.primaryBlue.withValues(alpha: 0.5);
          }
          return AppDesignSystem.backgroundCard;
        }),
      ),
      
      // 复选框主题
      checkboxTheme: CheckboxThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return AppDesignSystem.primaryBlue;
          }
          return Colors.transparent;
        }),
        checkColor: WidgetStateProperty.all(AppDesignSystem.textOnPrimary),
        side: const BorderSide(
          color: AppDesignSystem.borderPrimary,
          width: 2,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: AppDesignSystem.borderRadiusXS,
        ),
      ),
      
      // 单选按钮主题
      radioTheme: RadioThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return AppDesignSystem.primaryBlue;
          }
          return AppDesignSystem.textTertiary;
        }),
      ),
      
      // 滑块主题
      sliderTheme: SliderThemeData(
        activeTrackColor: AppDesignSystem.primaryBlue,
        inactiveTrackColor: AppDesignSystem.backgroundCard,
        thumbColor: AppDesignSystem.primaryBlue,
        overlayColor: AppDesignSystem.primaryBlue.withValues(alpha: 0.2),
        valueIndicatorColor: AppDesignSystem.primaryBlue,
        valueIndicatorTextStyle: AppDesignSystem.bodySmall.copyWith(
          color: AppDesignSystem.textOnPrimary,
        ),
      ),
      
      // 进度指示器主题
      progressIndicatorTheme: const ProgressIndicatorThemeData(
        color: AppDesignSystem.primaryBlue,
        linearTrackColor: AppDesignSystem.backgroundCard,
        circularTrackColor: AppDesignSystem.backgroundCard,
      ),
      
      // 浮动操作按钮主题
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: AppDesignSystem.primaryBlue,
        foregroundColor: AppDesignSystem.textOnPrimary,
        elevation: 8,
        shape: RoundedRectangleBorder(
          borderRadius: AppDesignSystem.borderRadiusL,
        ),
      ),
      
      // 芯片主题
      chipTheme: ChipThemeData(
        backgroundColor: AppDesignSystem.backgroundCard,
        selectedColor: AppDesignSystem.primaryBlue,
        disabledColor: AppDesignSystem.backgroundCard.withValues(alpha: 0.5),
        labelStyle: AppDesignSystem.bodySmall,
        secondaryLabelStyle: AppDesignSystem.bodySmall.copyWith(
          color: AppDesignSystem.textOnPrimary,
        ),
        padding: AppDesignSystem.paddingHorizontalS,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(18),
        ),
      ),
      
      // 工具提示主题
      tooltipTheme: TooltipThemeData(
        decoration: BoxDecoration(
          color: AppDesignSystem.backgroundCard,
          borderRadius: AppDesignSystem.borderRadiusS,
          boxShadow: [AppDesignSystem.shadowMedium],
        ),
        textStyle: AppDesignSystem.bodySmall,
        padding: AppDesignSystem.paddingS,
      ),
      
      // 系统状态栏样式
      extensions: [
        // _systemUiOverlayStyle,
      ],
    );
  }
  
  /// 浅色主题（备用主题）
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      
      colorScheme: ColorScheme.light(
        primary: AppDesignSystem.primaryBlue,
        primaryContainer: AppDesignSystem.primaryBlueAccent,
        secondary: AppDesignSystem.primaryBlueAccent,
        surface: Colors.white,
        surfaceContainer: const Color(0xFFF5F5F5),
        surfaceContainerHighest: const Color(0xFFE0E0E0),
        onPrimary: Colors.white,
        onSurface: Colors.black87,
        onSurfaceVariant: Colors.black54,
        outline: Colors.grey.shade300,
        error: AppDesignSystem.error,
      ),
      
      scaffoldBackgroundColor: Colors.white,
      
      // 其他主题配置可以根据需要添加
      textTheme: TextTheme(
        displayLarge: AppDesignSystem.headingLarge.copyWith(color: Colors.black87),
        displayMedium: AppDesignSystem.headingMedium.copyWith(color: Colors.black87),
        displaySmall: AppDesignSystem.headingSmall.copyWith(color: Colors.black87),
        bodyLarge: AppDesignSystem.bodyLarge.copyWith(color: Colors.black87),
        bodyMedium: AppDesignSystem.bodyMedium.copyWith(color: Colors.black87),
        bodySmall: AppDesignSystem.bodySmall.copyWith(color: Colors.black54),
      ),
    );
  }
  
  /// 系统UI覆盖样式
  static const SystemUiOverlayStyle _systemUiOverlayStyle = SystemUiOverlayStyle(
    statusBarColor: Colors.transparent,
    statusBarIconBrightness: Brightness.light,
    statusBarBrightness: Brightness.dark,
    systemNavigationBarColor: AppDesignSystem.backgroundPrimary,
    systemNavigationBarIconBrightness: Brightness.light,
  );
  
  /// 设置系统UI样式
  static void setSystemUIOverlayStyle() {
    SystemChrome.setSystemUIOverlayStyle(_systemUiOverlayStyle);
  }
}
